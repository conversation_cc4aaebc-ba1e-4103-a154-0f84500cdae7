/**
 * Helper functions cho tìm kiếm
 */
export class SearchHelper {
  /**
   * Chuyển đổi chuỗi có dấu thành không dấu
   * @param str Chuỗi cần chuyển đổi
   * @returns Chuỗi không dấu
   */
  static removeVietnameseAccents(str: string): string {
    if (!str) return '';
    
    return str
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Loại bỏ dấu
      .replace(/đ/g, 'd')
      .replace(/Đ/g, 'D')
      .toLowerCase();
  }

  /**
   * Tạo điều kiện tìm kiếm cho PostgreSQL với hỗ trợ tìm kiếm không dấu
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    
    // Tạo điều kiện tìm kiếm cho từng field
    const conditions = fields.map((field, index) => {
      const paramName = `search_${index}`;
      const normalizedParamName = `search_normalized_${index}`;
      
      return `(
        ${alias}.${field} ILIKE :${paramName} OR 
        unaccent(LOWER(${alias}.${field})) ILIKE :${normalizedParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;
    
    // Tạo parameters
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      parameters[`search_${index}`] = `%${searchTerm}%`;
      parameters[`search_normalized_${index}`] = `%${normalizedSearch}%`;
    });

    return { whereClause, parameters };
  }

  /**
   * Tạo điều kiện tìm kiếm đơn giản không cần extension unaccent
   * Sử dụng regex để loại bỏ dấu trong PostgreSQL
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createSimpleSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    
    // Tạo điều kiện tìm kiếm cho từng field
    const conditions = fields.map((field, index) => {
      const originalParamName = `search_original_${index}`;
      const normalizedParamName = `search_normalized_${index}`;
      
      return `(
        ${alias}.${field} ILIKE :${originalParamName} OR 
        LOWER(
          translate(
            ${alias}.${field}, 
            'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ',
            'aaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyydAAAAAAAAAAAAAAAAAEEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYYD'
          )
        ) ILIKE :${normalizedParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;
    
    // Tạo parameters
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      parameters[`search_original_${index}`] = `%${searchTerm}%`;
      parameters[`search_normalized_${index}`] = `%${normalizedSearch}%`;
    });

    return { whereClause, parameters };
  }

  /**
   * Tạo điều kiện tìm kiếm tối ưu nhất cho PostgreSQL
   * Sử dụng function có sẵn của PostgreSQL để xử lý dấu
   * @param searchTerm Từ khóa tìm kiếm
   * @param fields Danh sách các trường cần tìm kiếm
   * @param alias Alias của bảng trong query
   * @returns Object chứa whereClause và parameters
   */
  static createOptimizedSearchCondition(
    searchTerm: string,
    fields: string[],
    alias: string = 'entity'
  ): { whereClause: string; parameters: Record<string, any> } {
    if (!searchTerm || !fields.length) {
      return { whereClause: '', parameters: {} };
    }

    const normalizedSearch = this.removeVietnameseAccents(searchTerm);
    const timestamp = Date.now(); // Để tránh conflict parameter names

    // Tạo điều kiện tìm kiếm cho từng field với nhiều cách tìm kiếm
    const conditions = fields.map((field, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const lowerParamName = `searchLower_${timestamp}_${index}`;

      return `(
        ${alias}.${field} ILIKE :${originalParamName} OR
        ${alias}.${field} ILIKE :${normalizedParamName} OR
        LOWER(${alias}.${field}) ILIKE :${lowerParamName}
      )`;
    });

    const whereClause = `(${conditions.join(' OR ')})`;

    // Tạo parameters với nhiều pattern khác nhau
    const parameters: Record<string, any> = {};
    fields.forEach((_, index) => {
      const originalParamName = `searchOrig_${timestamp}_${index}`;
      const normalizedParamName = `searchNorm_${timestamp}_${index}`;
      const lowerParamName = `searchLower_${timestamp}_${index}`;

      parameters[originalParamName] = `%${searchTerm}%`;
      parameters[normalizedParamName] = `%${normalizedSearch}%`;
      parameters[lowerParamName] = `%${searchTerm.toLowerCase()}%`;
    });

    return { whereClause, parameters };
  }
}
